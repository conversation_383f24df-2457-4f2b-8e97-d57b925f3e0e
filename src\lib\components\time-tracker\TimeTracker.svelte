<script lang="ts">
	import { makeClient } from '$lib/make-client.js';
	import { onMount, onDestroy } from 'svelte';
	import { WSCommands } from '$lib/types/wsCommands';

	import ProjectSelector from '$lib/components/time-tracker/ProjectSelector.svelte';

	import * as Popover from '$lib/components/ui/popover/index.js';
	import { Input } from '$lib/components/ui/input/index.js';

	import SolarMenuDotsBold from '~icons/solar/menu-dots-bold';
	import type { time_entries } from '$lib/server/db/schema';

	const client = makeClient(fetch);

	let activeTimerEntry: typeof time_entries.$inferSelect | undefined = $state(undefined);
	let timerInterval: NodeJS.Timeout | null = null;
	let timerTotalTime = $state<number>(0);
	let eventSource: EventSource | null = null;
	let isConnecting = $state<boolean>(false);
	let reconnectAttempts = 0;
	let maxReconnectAttempts = 5;

	let timeEntryData = $derived.by(() => {
		if (!activeTimerEntry) return;

		return {
			description: activeTimerEntry.description,
			projectId: activeTimerEntry.project_id || '',
			start_time: new Date(activeTimerEntry.start_time).toTimeString().slice(0, 5),
			end_time: activeTimerEntry.end_time
		};
	});

	function calculateElapsedTime(startTime: Date): number {
		const now = new Date();
		const start = new Date(startTime);
		return Math.floor((now.getTime() - start.getTime()) / 1000); // Return seconds
	}

	function updateTimerDisplay() {
		if (activeTimerEntry && activeTimerEntry.start_time) {
			timerTotalTime = calculateElapsedTime(activeTimerEntry.start_time);
		}
	}

	async function startTimer(activeTimerData?: typeof time_entries.$inferSelect) {
		// Clear any existing timer first
		stopCountTimer();

		if (!activeTimerData) {
			// Check if there's already an active timer to prevent duplicates
			if (activeTimerEntry) {
				console.warn('Timer already active, not starting new one');
				return;
			}

			try {
				await client.tracker.start.$get();
			} catch (error) {
				console.error('Error starting timer:', error);
			}
		} else {
			activeTimerEntry = activeTimerData;
			// Calculate initial elapsed time
			updateTimerDisplay();

			// Update timer display every second
			timerInterval = setInterval(() => {
				updateTimerDisplay();
			}, 1000);
		}
	}

	async function stopTimer() {
		try {
			const response = await client.tracker.stop.$get();
			const endedTimeEntry = (await response.json()) as typeof time_entries.$inferSelect;

			activeTimerEntry = endedTimeEntry;

			// Dispatch custom event to notify other parts of the app
			if (typeof window !== 'undefined') {
				window.dispatchEvent(
					new CustomEvent('timer-stopped', {
						detail: { timeEntry: endedTimeEntry }
					})
				);
			}
		} catch (error) {
			console.error('Error stopping timer:', error);
			// Still stop the local timer even if server request fails
			stopCountTimer();
		}
	}

	function stopCountTimer() {
		if (timerInterval) {
			clearInterval(timerInterval);
			timerInterval = null;
		}
		timerTotalTime = 0;
		return (activeTimerEntry = undefined);
	}

	async function handleUpdateTimeEntry(id: string) {
		if (!timeEntryData) return;

		const { projectId, start_time, description } = timeEntryData;

		try {
			console.log('Data to update -> ', {
				id,
				projectId,
				start_time,
				description
			});

			function convertTimeToDate() {
				if (!start_time) return undefined;

				return new Date(
					new Date().setHours(
						Number(start_time.split(':')[0]),
						Number(start_time.split(':')[1]),
						0,
						0
					)
				);
			}

			const startTime = convertTimeToDate();

			const response = await client.tracker[':id'].$patch({
				param: { id },
				json: {
					projectId: projectId || undefined,
					start_time: start_time ? startTime : undefined,
					description: description || undefined
				}
			});

			if (!response.ok) {
				console.error('Failed to update time entry:', await response.json());
				return;
			}

			const [updatedEntry]: any = await response.json();
			console.log('updatedEntry -> ', updatedEntry);

			activeTimerEntry = updatedEntry;
		} catch (error) {
			console.error('Error updating time entry:', error);
		}
	}

	function cleanup() {
		// Clear timer interval
		if (timerInterval) {
			clearInterval(timerInterval);
			timerInterval = null;
		}

		// Close SSE connection
		if (eventSource) {
			console.log('Closing SSE connection');
			eventSource.close();
			eventSource = null;
		}

		// Reset connection state
		isConnecting = false;
		reconnectAttempts = 0;
	}

	function connectSSE() {
		// Prevent multiple simultaneous connections
		if (isConnecting || (eventSource && eventSource.readyState === EventSource.CONNECTING)) {
			console.log('SSE connection already in progress');
			return;
		}

		// Close existing connection if any
		if (eventSource) {
			eventSource.close();
			eventSource = null;
		}

		isConnecting = true;

		try {
			// Create SSE connection with credentials for authentication
			eventSource = new EventSource('/api/tracker/events', {
				withCredentials: true
			});

			eventSource.onopen = () => {
				console.log('SSE connection opened');
				isConnecting = false;
				reconnectAttempts = 0; // Reset on successful connection
			};

			eventSource.onmessage = (event) => {
				const payload = JSON.parse(event.data);
				console.log('Received SSE:', payload);

				console.log('payload -> ', payload);

				// Handle different event types
				switch (payload.type) {
					case 'connected':
						console.log('SSE connection confirmed, connectionId:', payload.connectionId);
						return;
					case 'heartbeat':
						// Heartbeat received, connection is alive
						return;
				}

				// Handle timer commands
				switch (payload.command as WSCommands) {
					case WSCommands.startTimer:
						startTimer(payload.data);
						console.log('SSE client callback Timer started');
						break;

					case WSCommands.stopTimer:
						stopCountTimer();
						console.log('SSE client callback Timer stop');
						// Dispatch custom event to notify other parts of the app
						if (typeof window !== 'undefined') {
							window.dispatchEvent(
								new CustomEvent('timer-stopped', {
									detail: { timeEntry: payload.data }
								})
							);
						}
						break;

					default:
						break;
				}
			};

			// Handle specific event types
			eventSource.addEventListener('connection', (event) => {
				const payload = JSON.parse(event.data);
				console.log('Connection event:', payload);
			});

			eventSource.addEventListener('timer-event', (event) => {
				const payload: { command: WSCommands; data: typeof time_entries.$inferSelect } = JSON.parse(
					event.data
				);

				// Handle timer commands from specific events
				switch (payload.command as WSCommands) {
					case WSCommands.startTimer:
						startTimer(payload.data);
						console.log('SSE timer-event callback Timer started');
						break;

					case WSCommands.stopTimer:
						stopCountTimer();
						console.log('SSE timer-event callback Timer stop');
						// Dispatch custom event to notify other parts of the app
						if (typeof window !== 'undefined') {
							window.dispatchEvent(
								new CustomEvent('timer-stopped', {
									detail: { timeEntry: payload.data }
								})
							);
						}
						break;
				}
			});

			eventSource.addEventListener('heartbeat', () => {
				// Heartbeat events to keep connection alive
				console.log('Heartbeat received');
			});

			eventSource.onerror = (error) => {
				console.error('SSE error:', error);
				isConnecting = false;

				// Handle reconnection with exponential backoff
				if (eventSource && eventSource.readyState === EventSource.CLOSED) {
					reconnectAttempts++;
					if (reconnectAttempts <= maxReconnectAttempts) {
						const delay = Math.min(1000 * Math.pow(2, reconnectAttempts - 1), 30000);
						console.log(
							`SSE reconnecting in ${delay}ms (attempt ${reconnectAttempts}/${maxReconnectAttempts})`
						);

						setTimeout(() => {
							if (!eventSource || eventSource.readyState === EventSource.CLOSED) {
								connectSSE();
							}
						}, delay);
					} else {
						console.error('Max SSE reconnection attempts reached. Please refresh the page.');
					}
				}
			};
		} catch (error) {
			console.error('Failed to create SSE connection:', error);
			isConnecting = false;
		}
	}

	onMount(() => {
		connectSSE();

		// Handle page visibility changes to manage connections
		const handleVisibilityChange = () => {
			if (document.hidden) {
				// Page is hidden, but keep connection alive
				console.log('Page hidden, keeping SSE connection alive');
			} else {
				// Page is visible, ensure connection is active
				console.log('Page visible, checking SSE connection');
				if (!eventSource || eventSource.readyState === EventSource.CLOSED) {
					connectSSE();
				}
			}
		};

		document.addEventListener('visibilitychange', handleVisibilityChange);

		// Cleanup on unmount
		return () => {
			document.removeEventListener('visibilitychange', handleVisibilityChange);
			cleanup();
		};
	});

	onDestroy(() => {
		cleanup();
		console.log('TimeTracker component destroyed');
	});

	// Handle page visibility changes to sync timer when user returns to tab
	if (typeof document !== 'undefined') {
		document.addEventListener('visibilitychange', () => {
			if (!document.hidden && activeTimerEntry) {
				// Page became visible, update timer display
				updateTimerDisplay();
			}
		});
	}

	// Handle browser close/refresh with active timer
	if (typeof window !== 'undefined') {
		window.addEventListener('beforeunload', (event) => {
			if (activeTimerEntry) {
				// Show warning if user tries to close with active timer
				// event.preventDefault();
				// // Modern browsers ignore the custom message, but we still need to return a string
				// return 'You have an active timer running. Are you sure you want to leave?';
			}
		});
	}
</script>

<div class="flex w-full flex-col gap-4 bg-slate-50/30">
	{#if timerTotalTime > 0 && timeEntryData}
		<div class="flex w-full gap-4 rounded-lg bg-stone-100 p-4">
			<form class="" onsubmit={() => handleUpdateTimeEntry(activeTimerEntry!.id)}>
				<div class="flex gap-2">
					<Input
						class="w-full"
						type="text"
						name="description"
						bind:value={timeEntryData.description}
						onfocusout={(el) => {
							handleUpdateTimeEntry(activeTimerEntry!.id);
						}}
					/>
					<!-- <Input
						class="w-full"
						name="projectId"
						type="text"
						bind:value={timeEntryData.projectId}
						onfocusout={(el) => {
							handleUpdateTimeEntry(activeTimerEntry!.id);
						}}
					/> -->

					<ProjectSelector
						bind:selectedProject={timeEntryData.projectId}
						onProjectSelected={()=> {
							handleUpdateTimeEntry(activeTimerEntry!.id);
						}}
					></ProjectSelector>
				</div>

				<Popover.Root>
					<Popover.Trigger>
						<!-- Counter -->
						<div class="font-mono text-2xl font-medium">
							<!-- Seconds to time format -->
							{Math.floor(timerTotalTime / 3600)
								.toString()
								.padStart(2, '0')}:{Math.floor((timerTotalTime % 3600) / 60)
								.toString()
								.padStart(2, '0')}:{(timerTotalTime % 60).toString().padStart(2, '0')}
						</div></Popover.Trigger
					>
					<Popover.Content>
						Start time:
						<input
							type="time"
							name="start_time"
							bind:value={timeEntryData.start_time}
							class="rounded-lg border p-2"
							onfocusout={(el) => {
								handleUpdateTimeEntry(activeTimerEntry!.id);
							}}
							onkeydown={(event) => {
								if (event.key === 'Enter') {
									handleUpdateTimeEntry(activeTimerEntry!.id);
								}
							}}
						/></Popover.Content
					>
				</Popover.Root>

				<button type="submit">Update</button>
			</form>
		</div>

		<pre class="absolute bottom-[100%] left-0 bg-white/20">{JSON.stringify(
				activeTimerEntry,
				null,
				2
			)}</pre>
	{/if}

	<div class="">
		<!-- {#if timerTotalTime > 0} -->
		<button class="bg-red-200 p-4" onclick={() => stopTimer()}>Stop Timer</button>
		<!-- {:else} -->
		<button class="bg-green-200 p-4" onclick={() => startTimer()}>Start Timer</button>
		<!-- {/if} -->
	</div>
</div>
