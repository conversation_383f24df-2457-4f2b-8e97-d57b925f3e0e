import { Hono } from 'hono';
import { cors } from 'hono/cors';
import { tasksRouter } from './tasks';
import { authRouter } from './auth';
import { trackerRouter } from './tracker';
import { projectsRouter } from './manager/projects';

const app = new Hono()
	.use(
		'*',
		cors({
			origin: 'http://localhost:5173',
			allowHeaders: ['Content-Type', 'Authorization'],
			allowMethods: ['POST', 'GET', 'PUT', 'DELETE', 'PATCH', 'OPTIONS', 'HEAD'],
			credentials: true,
		})
	)
	.route('/tasks', tasksRouter)
	.route('/auth', authRouter)
	.route('/tracker', trackerRouter)
	.route('/manager/projects', projectsRouter);

export const api = new Hono().route('/api', app);

export type Router = typeof app;

// @ts-expect-error
if (!globalThis.__bun_server__) {
	// @ts-expect-error
	globalThis.__bun_server__ = Bun.serve({
		port: 4000,
		fetch: api.fetch,
		development: true
	});
}
