<script lang="ts">
	import { makeClient } from '$lib/make-client.js';
	const client = makeClient(fetch);

	let isLoading = $state(false);
	let projectName = $state(`Test project ${Math.random().toFixed(3)}`);
	let projectDescription = $state('Small description');
	let errorMessage = $state('');

	let projects = $state(loadProjects());

	async function handleCreateProject() {
		try {
			isLoading = true;
			errorMessage = ''; // Clear any previous error

			const response = await client.manager.projects.$post({
				json: {
					name: projectName,
					description: projectDescription
				}
			});

			if (!response.ok) {
				const errorData = await response.json();
				if ('error' in errorData && errorData.error === 'DUPLICATE_PROJECT_NAME') {
					errorMessage = 'Project with this name already exists. Please choose a different name.';
					return;
				}
				errorMessage = 'Failed to create project';
				return;
			}

			// Success - clear the form
			projectName = `Test project ${Math.random().toFixed(3)}`;
			projectDescription = 'Small description';
		} catch (error) {
			console.log('Error creating project: ', error);
			errorMessage = 'An unexpected error occurred while creating the project';
		} finally {
			isLoading = false;
			refreshProjects();
		}
	}

	async function handleUpdateProject({
		id,
		formData
	}: {
		id: string;
		formData: FormData;
	}) {
		try {

			const name = formData.get('name') as string;
			const description = formData.get('description') as string;

			isLoading = true;
			errorMessage = '';

			if (name.length === 0) {
				errorMessage = 'Please enter a name for the project';
				return;
			}

			const response = await client.manager.projects[':id'].$patch({
				param: { id },
				json: {
					name,
					description: description || undefined
				}
			});

			if (!response.ok) {
				errorMessage = 'Failed to update project';
			}
		} catch (error) {
			console.log('Error updating project: ', error);
			errorMessage = 'An unexpected error occurred while updating the project';
		} finally {
			isLoading = false;
			refreshProjects();
		}
	}

	async function handleDeleteProject(id: string) {
		try {
			isLoading = true;
			errorMessage = '';

			const response = await client.manager.projects[':id'].$delete({
				param: { id }
			});

			if (!response.ok) {
				errorMessage = 'Failed to delete project';
			}
		} catch (error) {
			console.log('Error deleting project: ', error);
			errorMessage = 'An unexpected error occurred while deleting the project';
		} finally {
			isLoading = false;
			refreshProjects();
		}
	}

	function refreshProjects() {
		return (projects = loadProjects());
	}

	async function loadProjects() {
		const projects = await client.manager.projects.$get();
		return await projects.json();
	}
</script>

<div class="container">
	<!-- Create project -->
	<div class="flex w-full flex-col gap-4">
		<input type="text" bind:value={projectName} class="rounded-lg border p-2" />
		<input type="text" bind:value={projectDescription} class="rounded-lg border p-2" />
		<button type="button" onclick={() => handleCreateProject()} disabled={isLoading}>
			{isLoading ? 'Creating...' : 'Create Project'}
		</button>

		{#if errorMessage}
			<div class="rounded-lg border border-red-400 bg-red-100 px-4 py-3 text-red-700">
				{errorMessage}
			</div>
		{/if}
	</div>

	<button
		onclick={() => {
			console.log(projectName);
		}}
	>
		Check form data
	</button>

	<button class="bg-blue-100 p-4" onclick={refreshProjects}>Refresh projects</button>

	<div class="space-y-4">
		{#await projects then projectsData}
			{#each projectsData.data as project (project.id)}
				<form
				class="p-4 bg-slate-50/30 rounded-lg"
					id={project.id}
					onsubmit={(el) =>{
						const form = el.target as HTMLFormElement;
						handleUpdateProject({
							id: project.id,
							formData: new FormData(form)
						})}}
				>
					<div class="text-sm">{project.id}</div>
					<input name="name" class="mt-1 bg-slate-100 p-1" type="text" value={project.name} />
					<input name="description" class="mt-1 bg-slate-100 p-1" type="text" bind:value={project.description} />
					<button type="submit">Update</button>
					<button type="button" onclick={() => handleDeleteProject(project.id)}>Delete</button>
				</form>
			{/each}
		{/await}
	</div>
</div>
