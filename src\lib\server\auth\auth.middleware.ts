import { getCookie, setCookie } from 'hono/cookie';
import { validateSession } from './auth.service';
import type { AuthResponse } from './auth.types';

// Middleware to validate session ID
export const authMiddleware = async (c: any, next: () => Promise<void>) => {
	const sessionId = getCookie(c, 'session');
	if (!sessionId) {
		return c.json({ success: false, error: 'No session ID provided' } as AuthResponse, 401);
	}

	const validateResult = await validateSession(sessionId);
	if (!validateResult.success) {
		setCookie(c, 'session', '', { expires: new Date(0) });
		return c.json(validateResult as AuthResponse, 401);
	}
	
	c.set('authUser', validateResult.data);

	await next();
};
