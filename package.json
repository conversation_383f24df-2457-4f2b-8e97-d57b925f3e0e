{"name": "time-tracker", "private": true, "version": "0.0.1", "type": "module", "scripts": {"dev": "vite dev", "build": "vite build", "preview": "vite preview", "prepare": "svelte-kit sync || echo ''", "check": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json", "check:watch": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json --watch", "format": "prettier --write .", "lint": "prettier --check .", "db:start": "docker compose up", "db:push": "drizzle-kit push", "db:migrate": "drizzle-kit migrate", "db:studio": "drizzle-kit studio"}, "devDependencies": {"@iconify/json": "^2.2.365", "@iconify/svelte": "^5.0.1", "@internationalized/date": "^3.8.1", "@lucide/svelte": "^0.515.0", "@sveltejs/adapter-auto": "^6.0.1", "@sveltejs/kit": "^2.27.0", "@sveltejs/vite-plugin-svelte": "^5.1.1", "@tailwindcss/vite": "^4.1.11", "@types/bun": "^1.2.19", "@types/node": "^20.19.9", "bits-ui": "^2.8.6", "clsx": "^2.1.1", "dotenv": "^17.2.1", "drizzle-kit": "^0.30.6", "prettier": "^3.6.2", "prettier-plugin-svelte": "^3.4.0", "prettier-plugin-tailwindcss": "^0.6.14", "svelte": "^5.37.3", "svelte-check": "^4.3.1", "tailwind-variants": "^2.1.0", "tailwindcss": "^4.1.11", "tw-animate-css": "^1.3.6", "typescript": "^5.9.2", "unplugin-icons": "^22.2.0", "vite": "^6.3.5"}, "dependencies": {"@floating-ui/dom": "^1.7.3", "@hono/zod-validator": "^0.7.2", "@inlang/paraglide-js": "^2.2.0", "@types/jsonwebtoken": "^9.0.10", "drizzle-orm": "^0.40.1", "hono": "^4.8.12", "iconify-icon": "^3.0.0", "jsonwebtoken": "^9.0.2", "openid-client": "^6.6.2", "postgres": "^3.4.7", "tailwind-merge": "^3.3.1", "zod": "^3.25.76"}}