import {
	eq,
	and,
	sql,
	count,
	asc,
	desc,
	gt,
	lt,
	gte,
	lte,
	between,
	ne,
	isNotNull
} from 'drizzle-orm';
import { db } from '../db';

import { projects } from '../db/schema';
export async function createProject(
	authUserId: string,
	name: string,
	description?: string
): Promise<typeof projects.$inferSelect> {
    
	const [project] = await db
		.insert(projects)
		.values({ auth_user_id: authUserId, name, description })
		.returning();

	return project;
}

export async function updateProject(
	projectId: string,
	authUserId: string,
	data: {
		name?: string;
		description?: string;
	}
): Promise<typeof projects.$inferSelect> {
	const [project] = await db
		.update(projects)
		.set(data)
		.where(and(eq(projects.id, projectId), eq(projects.auth_user_id, authUserId)))
		.returning();
	if (!project) {
		throw new Error('Project not found or unauthorized');
	}

	return project;
}

export async function deleteProject(projectId: string, authUserId: string): Promise<boolean> {
	const [deletedProject] = await db
		.delete(projects)
		.where(and(eq(projects.id, projectId), eq(projects.auth_user_id, authUserId)))
		.returning();
	if (!deletedProject) {
		throw new Error('Project not found or unauthorized');
	}
	return true;
}

export async function getAllUserProjects(
	authUserId: string
): Promise<{ usersProjects: (typeof projects.$inferSelect)[]; total: number }> {
	const usersProjects = await db
		.select()
		.from(projects)
		.where(eq(projects.auth_user_id, authUserId))
		.orderBy(desc(projects.created_at));

	const totalQuery = db
		.select({ count: count() })
		.from(projects)
		.where(eq(projects.auth_user_id, authUserId));
	const totalResult = await totalQuery;

	const total = totalResult[0].count;

	return { usersProjects, total };
}

export async function getProjectById(projectId: string): Promise<typeof projects.$inferSelect> {
	const [project] = await db.select().from(projects).where(eq(projects.id, projectId));
	if (!project) {
		throw new Error('Project not found');
	}
	return project;
}
