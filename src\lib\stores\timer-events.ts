import { writable } from 'svelte/store';

// Store to track timer events
export const timerEvents = writable<{
	type: 'timer-stopped' | 'timer-started';
	timestamp: number;
	data?: any;
} | null>(null);

// Helper function to dispatch timer stopped event
export function dispatchTimerStopped(timeEntry?: any) {
	timerEvents.set({
		type: 'timer-stopped',
		timestamp: Date.now(),
		data: timeEntry
	});
}

// Helper function to dispatch timer started event
export function dispatchTimerStarted(timeEntry?: any) {
	timerEvents.set({
		type: 'timer-started',
		timestamp: Date.now(),
		data: timeEntry
	});
}
