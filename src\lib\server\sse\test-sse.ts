/**
 * Simple test utility to verify SSE connection manager functionality
 * This can be used for debugging and testing the SSE implementation
 */

import { sseConnectionManager } from './connection-manager';

// Mock SSE stream for testing
class MockSSEStream {
	private messages: any[] = [];
	
	async writeSSE(data: any): Promise<void> {
		this.messages.push({
			timestamp: new Date().toISOString(),
			data
		});
		console.log('Mock SSE Write:', data);
	}
	
	getMessages() {
		return this.messages;
	}
	
	clearMessages() {
		this.messages = [];
	}
}

export function testSSEConnectionManager() {
	console.log('🧪 Testing SSE Connection Manager...');
	
	// Test 1: Add connections
	const mockStream1 = new MockSSEStream();
	const mockStream2 = new MockSSEStream();
	
	const connectionId1 = sseConnectionManager.addConnection('user1', mockStream1);
	const connectionId2 = sseConnectionManager.addConnection('user1', mockStream2);
	const connectionId3 = sseConnectionManager.addConnection('user2', new MockSSEStream());
	
	console.log('✅ Added 3 connections');
	
	// Test 2: Check stats
	const stats = sseConnectionManager.getStats();
	console.log('📊 Stats:', stats);
	
	// Test 3: Broadcast to user
	sseConnectionManager.broadcastToUser('user1', {
		command: 'startTimer',
		data: { id: 'test-timer', description: 'Test Timer' }
	});
	
	console.log('📡 Broadcasted to user1');
	console.log('📨 Stream1 messages:', mockStream1.getMessages());
	console.log('📨 Stream2 messages:', mockStream2.getMessages());
	
	// Test 4: Remove connection
	const removed = sseConnectionManager.removeConnection('user1', connectionId1);
	console.log('🗑️ Removed connection:', removed);
	
	// Test 5: Final stats
	const finalStats = sseConnectionManager.getStats();
	console.log('📊 Final stats:', finalStats);
	
	console.log('✅ SSE Connection Manager test completed!');
}

// Export for use in development/debugging
export { MockSSEStream };
