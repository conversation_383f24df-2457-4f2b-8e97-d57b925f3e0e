<script lang="ts">
	import { makeClient } from '$lib/make-client.js';
	import * as DropdownMenu from '$lib/components/ui/dropdown-menu/index.js';

	let {
		onProjectSelected,
		selectedProject = $bindable()
	}: {
		onProjectSelected: () => void;
		selectedProject?: string;
	} = $props();

	const client = makeClient(fetch);
	let projects = $state(getProjects());

	async function getProjects() {
		const projects = await client.manager.projects.$get();
		const { data } = await projects.json();
		return data;
	}
	async function refreshProjects() {
		projects = getProjects();
	}
</script>

<DropdownMenu.Root
	onOpenChange={(state) => {
		state && refreshProjects();
	}}
>
	<DropdownMenu.Trigger>
		{#snippet child({ props })}
			<button {...props}>
				{#await projects then projects}
					<span
						>{projects.find((project) => project.id === selectedProject)?.name ||
							'Add project'}</span
					>
				{/await}
			</button>
		{/snippet}
	</DropdownMenu.Trigger>

	{#await projects then projects}
		<DropdownMenu.Content class="w-56">
			<DropdownMenu.Group>
				<DropdownMenu.Label>Panel Position</DropdownMenu.Label>
				<DropdownMenu.Separator />
				<DropdownMenu.RadioGroup
					bind:value={selectedProject}
					onValueChange={() => {
						console.log('Project selected:', selectedProject);
						// onProjectSelected();
					}}
				>
					<DropdownMenu.RadioItem value={''}>Without project</DropdownMenu.RadioItem>
					{#each projects as project}
						<DropdownMenu.RadioItem value={project.id}>{project.name}</DropdownMenu.RadioItem>
					{/each}
				</DropdownMenu.RadioGroup>
			</DropdownMenu.Group>
		</DropdownMenu.Content>
	{/await}
</DropdownMenu.Root>
