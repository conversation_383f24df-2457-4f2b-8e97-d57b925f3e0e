import { authMiddleware } from '$lib/server/auth/auth.middleware';
import type { AuthResponse } from '$lib/server/auth/auth.types';
import { WSCommands } from '$lib/types/wsCommands';
import {
	createActiveTimer,
	createTimeEntry,
	getActiveTimerForUser,
	getAllTimeEntriesForUser,
	stopUserTimer,
	updateTimeEntry
} from '$lib/server/time-entries/time-entries.service';
import { zValidator } from '@hono/zod-validator';
import { Hono } from 'hono';
import { streamSSE } from 'hono/streaming';
import { z } from 'zod';
import { client as redisClient } from '$lib/server/db/redis-client';
import { sseConnectionManager } from '$lib/server/sse/connection-manager';

export const trackerRouter = new Hono<{ Variables: { authUser: AuthResponse['data'] } }>()
	.use('*', authMiddleware)
	.get('/', async (c) => {
		const authUser = c.get('authUser');
		const authUserId = authUser?.id;

		// Parse query params for filter, sort, pagination
		const query = c.req.query();
		const page = parseInt(query.page) || 1;
		const pageSize = parseInt(query.pageSize) || 10;
		const sort = query.sort || 'created_at:desc';

		// Parse filters with operators
		const filters = [];
		for (const [key, value] of Object.entries(query)) {
			if (key !== 'page' && key !== 'limit' && key !== 'sort') {
				const [field, operator = 'eq'] = key.split('__'); // Default to 'eq' if no operator
				if (operator === 'between') {
					const [start, end] = value.split(',');
					filters.push({ field, operator, value: [start, end] });
				} else {
					filters.push({ field, operator, value });
				}
			}
		}

		const { entries, total } = await getAllTimeEntriesForUser(
			authUserId,
			page,
			pageSize,
			sort,
			filters
		);

		return c.json({ data: entries, total, randomNumber: Math.random() });
	})
	.post('/', async (c) => {
		// Get authUserId from authMiddleware
		const authUser = c.get('authUser');
		const authUserId = authUser?.id;

		const createdTimeEntry = await createTimeEntry(
			authUserId,
			new Date(),
			new Date('2025-07-14T12:00:00Z'),
			`Description ${Math.random().toPrecision(3)}`,
			['tag 1', 'tag 2']
		);
		return c.json(createdTimeEntry);
	})
	.get('/start', async (c) => {
		// Get authUserId from authMiddleware
		const authUser = c.get('authUser');
		const authUserId = authUser?.id;

		const activeTimer = await getActiveTimerForUser(authUserId);
		if (activeTimer) {
			return c.json(
				{
					message: 'User already has an active timer running. Please stop the current timer first.'
				},
				500
			);
		}

		const createdTimeEntry = await createTimeEntry(
			authUserId,
			new Date(),
			undefined,
			`Timer ${Math.random().toPrecision(3)}`
		);

		if (!createdTimeEntry) {
			// Return error
			return c.json({ message: 'Failed to create time entry' }, 500);
		}

		const createdActiveTimer = await createActiveTimer(authUserId, createdTimeEntry.id);

		// Broadcast timer start to all user's SSE connections
		await sseConnectionManager.broadcastToUser(authUserId, {
			command: WSCommands.startTimer,
			data: createdTimeEntry
		});

		return c.json(createdActiveTimer);
	})
	.get('/stop', async (c) => {
		// Get authUserId from authMiddleware
		const authUser = c.get('authUser');
		const authUserId = authUser?.id;
		let endedTimeEntry: any = null;

		try {
			endedTimeEntry = await stopUserTimer(authUserId);

			// Broadcast timer stop to all user's SSE connections
			await sseConnectionManager.broadcastToUser(authUserId, {
				command: WSCommands.stopTimer,
				data: endedTimeEntry
			});

			return c.json(endedTimeEntry);
		} catch (err) {
			console.log(err);

			// Still broadcast stop command even if there was an error
			await sseConnectionManager.broadcastToUser(authUserId, {
				command: WSCommands.stopTimer
			});

			return c.json({ message: 'Timer stopped' }, 500);
		}
	})
	.patch(
		'/:id',
		zValidator(
			'json',
			z.object({
				projectId: z.string().optional(),
				start_time: z.coerce.date().optional(),
				end_time: z.coerce.date().optional(),
				description: z.string().optional(),
				tags: z.array(z.string()).optional()
			})
		),
		async (c) => {
			const authUser = c.get('authUser');
			const authUserId = authUser?.id;

			const { id } = c.req.param();
			const data = c.req.valid('json');

			console.log('data update timeEntry -> ', data);

			const updatedTimeEntry = await updateTimeEntry({
				authUserId,
				timeEntryId: id,
				...data
			});
			return c.json(updatedTimeEntry);
		}
	);

trackerRouter.get('/events', async (c) => {
	const authUser = c.get('authUser');
	const authUserId = authUser?.id;

	if (!authUserId) {
		return c.json({ error: 'Unauthorized' }, 401);
	}

	return streamSSE(c, async (stream) => {
		let connectionId: string | null = null;
		let heartbeatInterval: NodeJS.Timeout | null = null;

		try {
			// Add connection to manager with Hono's SSE stream
			connectionId = sseConnectionManager.addConnection(authUserId, {
				writeSSE: async (data: any) => {
					await stream.writeSSE({
						data: JSON.stringify(data),
						event: 'timer-event'
					});
				}
			});

			// Handle client disconnect
			stream.onAbort(() => {
				if (connectionId) {
					sseConnectionManager.removeConnection(authUserId, connectionId);
					console.log(`SSE connection closed for user ${authUserId}, connectionId: ${connectionId}`);
				}
				if (heartbeatInterval) {
					clearInterval(heartbeatInterval);
				}
			});

			// Send initial connection confirmation
			await stream.writeSSE({
				data: JSON.stringify({
					type: 'connected',
					connectionId
				}),
				event: 'connection',
				id: connectionId
			});

			// Send initial active timer state
			const activeTimer = await getActiveTimerForUser(authUserId);
			if (activeTimer) {
				await stream.writeSSE({
					data: JSON.stringify({
						command: WSCommands.startTimer,
						data: activeTimer.time_entry
					}),
					event: 'timer-event',
					id: `timer-${Date.now()}`
				});
			}

			console.log(`SSE connection opened for user ${authUserId}, connectionId: ${connectionId}`);

			// Keep connection alive with periodic heartbeat
			heartbeatInterval = setInterval(async () => {
				try {
					await stream.writeSSE({
						data: JSON.stringify({ type: 'heartbeat', timestamp: Date.now() }),
						event: 'heartbeat'
					});
				} catch (error) {
					console.error('Heartbeat failed:', error);
					if (heartbeatInterval) {
						clearInterval(heartbeatInterval);
						heartbeatInterval = null;
					}
				}
			}, 30000); // Every 30 seconds

			// Keep the stream alive - this is important for SSE
			await new Promise<void>((resolve) => {
				stream.onAbort(() => {
					resolve();
				});

				// Handle any stream errors
				c.req.raw.signal.addEventListener('abort', () => {
					resolve();
				});
			});

		} catch (error) {
			console.error('SSE connection error:', error);
			// Clean up on error
			if (connectionId) {
				sseConnectionManager.removeConnection(authUserId, connectionId);
			}
			if (heartbeatInterval) {
				clearInterval(heartbeatInterval);
			}
			throw error;
		}
	}, async (err) => {
		console.error('SSE stream error:', err);
		// Error handling - connection will be automatically cleaned up
	});
})
.get('/sse-stats', async (c) => {
	const authUser = c.get('authUser');
	const authUserId = authUser?.id;

	if (!authUserId) {
		return c.json({ error: 'Unauthorized' }, 401);
	}

	const stats = sseConnectionManager.getStats();
	return c.json({
		...stats,
		userConnections: authUserId in stats.userConnections ? { [authUserId]: stats.userConnections[authUserId] } : {},
		timestamp: new Date().toISOString()
	});
});
// .post('/:id/finish', zValidator('param', TaskParam), (c) => {
// 	const { id } = c.req.valid('param');
// 	const task = tasks.find((task) => task.id === id);
// 	if (task) {
// 		task.done = true;
// 		return c.json(task);
// 	}

// 	throw c.json({ message: 'Task not found' }, 404);
// })
