import type { Context } from 'hono';

export interface SSEConnection {
	userId: string;
	stream: { writeSSE: (data: any) => Promise<void> };
	connectionId: string;
	connectedAt: Date;
}

class SSEConnectionManager {
	private connections = new Map<string, Set<SSEConnection>>();

	/**
	 * Add a new SSE connection for a user
	 */
	addConnection(userId: string, stream: { writeSSE: (data: any) => Promise<void> }): string {
		const connectionId = crypto.randomUUID();
		const connection: SSEConnection = {
			userId,
			stream,
			connectionId,
			connectedAt: new Date()
		};

		if (!this.connections.has(userId)) {
			this.connections.set(userId, new Set());
		}

		const userConnections = this.connections.get(userId)!;

		// Limit connections per user to prevent runaway connections
		const maxConnectionsPerUser = 10;
		if (userConnections.size >= maxConnectionsPerUser) {
			console.warn(`User ${userId} has reached max connections (${maxConnectionsPerUser}), removing oldest`);
			const oldestConnection = Array.from(userConnections)[0];
			userConnections.delete(oldestConnection);
		}

		userConnections.add(connection);
		console.log(`SSE connection added for user ${userId}, connectionId: ${connectionId} (total: ${userConnections.size})`);

		return connectionId;
	}

	/**
	 * Remove a specific connection
	 */
	removeConnection(userId: string, connectionId: string): boolean {
		const userConnections = this.connections.get(userId);
		if (!userConnections) return false;

		for (const connection of userConnections) {
			if (connection.connectionId === connectionId) {
				userConnections.delete(connection);
				console.log(`SSE connection removed for user ${userId}, connectionId: ${connectionId}`);
				
				// Clean up empty user sets
				if (userConnections.size === 0) {
					this.connections.delete(userId);
				}
				return true;
			}
		}
		return false;
	}

	/**
	 * Get all connections for a specific user
	 */
	getUserConnections(userId: string): Set<SSEConnection> {
		return this.connections.get(userId) || new Set();
	}

	/**
	 * Broadcast a message to all connections for a specific user
	 */
	async broadcastToUser(userId: string, data: any): Promise<void> {
		const userConnections = this.getUserConnections(userId);
		if (userConnections.size === 0) {
			console.log(`No SSE connections found for user ${userId}`);
			return;
		}

		const deadConnections: SSEConnection[] = [];

		for (const connection of userConnections) {
			try {
				await connection.stream.writeSSE(data);
			} catch (error) {
				console.error(`Failed to write to SSE connection ${connection.connectionId}:`, error);
				deadConnections.push(connection);
			}
		}

		// Remove dead connections
		for (const deadConnection of deadConnections) {
			this.removeConnection(userId, deadConnection.connectionId);
		}

		console.log(`Broadcasted to ${userConnections.size - deadConnections.length} SSE connections for user ${userId}`);
	}

	/**
	 * Send heartbeat to all connections to keep them alive
	 */
	async sendHeartbeat(): Promise<void> {
		for (const [userId, userConnections] of this.connections) {
			const deadConnections: SSEConnection[] = [];

			for (const connection of userConnections) {
				try {
					await connection.stream.writeSSE({
						type: 'heartbeat',
						timestamp: Date.now()
					});
				} catch (error) {
					console.error(`Heartbeat failed for connection ${connection.connectionId}:`, error);
					deadConnections.push(connection);
				}
			}

			// Remove dead connections
			for (const deadConnection of deadConnections) {
				this.removeConnection(userId, deadConnection.connectionId);
			}
		}
	}

	/**
	 * Get connection statistics
	 */
	getStats(): { totalUsers: number; totalConnections: number; userConnections: Record<string, number> } {
		const userConnections: Record<string, number> = {};
		let totalConnections = 0;

		for (const [userId, connections] of this.connections) {
			userConnections[userId] = connections.size;
			totalConnections += connections.size;
		}

		return {
			totalUsers: this.connections.size,
			totalConnections,
			userConnections
		};
	}

	/**
	 * Clean up stale connections (older than specified minutes)
	 */
	cleanupStaleConnections(maxAgeMinutes: number = 60): void {
		const cutoffTime = new Date(Date.now() - maxAgeMinutes * 60 * 1000);
		let removedCount = 0;

		for (const [userId, userConnections] of this.connections) {
			const staleConnections: SSEConnection[] = [];

			for (const connection of userConnections) {
				if (connection.connectedAt < cutoffTime) {
					staleConnections.push(connection);
				}
			}

			for (const staleConnection of staleConnections) {
				userConnections.delete(staleConnection);
				removedCount++;
			}

			// Clean up empty user sets
			if (userConnections.size === 0) {
				this.connections.delete(userId);
			}
		}

		if (removedCount > 0) {
			console.log(`Cleaned up ${removedCount} stale SSE connections`);
		}
	}

	/**
	 * Clean up all connections (useful for server shutdown)
	 */
	cleanup(): void {
		this.connections.clear();
		console.log('All SSE connections cleaned up');
	}
}

// Create singleton instance
export const sseConnectionManager = new SSEConnectionManager();

// Periodic cleanup of stale connections (every 5 minutes)
setInterval(() => {
	sseConnectionManager.cleanupStaleConnections(30); // Remove connections older than 30 minutes
}, 5 * 60 * 1000);
