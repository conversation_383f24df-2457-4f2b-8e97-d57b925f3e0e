<script lang="ts">
	import type { Snippet } from 'svelte';
	import type { LayoutData } from './$types';
	import Header from '$lib/components/Header.svelte';
	import TabBar from '$lib/components/navigation/TabBar.svelte';

	let { data, children }: { data: LayoutData; children: Snippet } = $props();

	import { page } from '$app/state';
	import { beforeNavigate, goto } from '$app/navigation';
	import { makeClient } from '$lib/make-client';

	beforeNavigate(async ({ to, cancel }) => {
		const client = makeClient(fetch);
		const validateResult = await client.auth.validate.$get();
		if (to?.route.id?.startsWith('/(dashboard)/') && !validateResult.ok) {
			cancel();
			goto('/login');
		}
	});
</script>

<!-- <Header user={{ name: '<PERSON>' }} /> -->
{@render children()}
<TabBar />
