import { authMiddleware } from '$lib/server/auth/auth.middleware';
import type { AuthResponse } from '$lib/server/auth/auth.types';
import { WSCommands } from '$lib/types/wsCommands';
import {
	createProject,
	updateProject,
	deleteProject,
	getAllUserProjects,
	getProjectById
} from '$lib/server/projects/projects.service';
import { zValidator } from '@hono/zod-validator';
import { Hono } from 'hono';
import { z } from 'zod';

export const projectsRouter = new Hono<{ Variables: { authUser: AuthResponse['data'] } }>()
	.use('*', authMiddleware)
	.get('/', async (c) => {
		const authUser = c.get('authUser');
		const authUserId = authUser?.id;

		const { usersProjects, total } = await getAllUserProjects(authUserId);

		return c.json({ data: usersProjects, total });
	})
	.get('/:id', async (c) => {
		const { id } = c.req.param();

		try {
			const project = await getProjectById(id);
			return c.json(project);
		} catch (error: any) {
			// Handle project not found
			if (error?.message === 'Project not found') {
				return c.json({ message: error.message }, 404);
			}

			console.error('Error fetching project:', error);
			return c.json({ message: 'Failed to fetch project' }, 500);
		}
	})
	.post('/', async (c) => {
		const authUser = c.get('authUser');
		const authUserId = authUser?.id;

		const { name, description } = await c.req.json();

		try {
			const createdProject = await createProject(authUserId, name, description);

			if (!createdProject) {
				return c.json({ message: 'Failed to create project' }, 500);
			}

			return c.json(createdProject);
		} catch (error: any) {
			// Handle PostgreSQL unique constraint violation for project name
			if (error?.code === 'ERR_POSTGRES_SERVER_ERROR' && error?.errno === '23505') {
				if (error?.constraint === 'projects_name_unique') {
					return c.json(
						{
							message: 'A project with this name already exists. Please choose a different name.',
							error: 'DUPLICATE_PROJECT_NAME'
						},
						409
					);
				}
			}

			// Handle other database errors
			console.error('Error creating project:', error);
			return c.json({ message: 'Failed to create project' }, 500);
		}
	})
	.patch(
		'/:id',
		zValidator(
			'json',
			z.object({
				name: z.string().optional(),
				description: z.string().optional()
			})
		),
		async (c) => {
			const authUser = c.get('authUser');
			const authUserId = authUser?.id;

			const { id } = c.req.param();
			const { name, description } = c.req.valid('json');

			try {
				const updatedProject = await updateProject(id, authUserId, { name, description });
				return c.json(updatedProject);
			} catch (error: any) {
				// Handle PostgreSQL unique constraint violation for project name
				if (error?.code === 'ERR_POSTGRES_SERVER_ERROR' && error?.errno === '23505') {
					if (error?.constraint === 'projects_name_unique') {
						return c.json(
							{
								message: 'A project with this name already exists. Please choose a different name.',
								error: 'DUPLICATE_PROJECT_NAME'
							},
							409
						);
					}
				}

				// Handle other errors (like project not found)
				if (error?.message === 'Project not found or unauthorized') {
					return c.json({ message: error.message }, 404);
				}

				console.error('Error updating project:', error);
				return c.json({ message: 'Failed to update project' }, 500);
			}
		}
	)
	.delete('/:id', async (c) => {
		const authUser = c.get('authUser');
		const authUserId = authUser?.id;

		const { id } = c.req.param();

		try {
			const deleted = await deleteProject(id, authUserId);
			return c.json({ deleted });
		} catch (error: any) {
			// Handle project not found or unauthorized
			if (error?.message === 'Project not found or unauthorized') {
				return c.json({ message: error.message }, 404);
			}

			console.error('Error deleting project:', error);
			return c.json({ message: 'Failed to delete project' }, 500);
		}
	});
