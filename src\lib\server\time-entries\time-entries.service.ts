import {
	eq,
	and,
	sql,
	count,
	asc,
	desc,
	gt,
	lt,
	gte,
	lte,
	between,
	ne,
	isNotNull
} from 'drizzle-orm';
import { db } from '../db';
import { time_entries } from '../db/schema';
import { client as redisClient } from '$lib/server/db/redis-client';

export async function createTimeEntry(
	authUserId: string,
	startTime: Date,
	endTime?: Date,
	description?: string,
	tags: string[] = []
): Promise<typeof time_entries.$inferSelect> {
	const [timeEntry] = await db
		.insert(time_entries)
		.values({
			auth_user_id: authUserId,
			start_time: startTime,
			end_time: endTime,
			description,
			tags
		})
		.returning();

	return timeEntry;
}

export async function createActiveTimer(authUserId: string, timeEntryId: string): Promise<any> {
	const activeTimer = {
		auth_user_id: authUserId,
		time_entry_id: timeEntryId
	};

	await redisClient.set(`active_timer:${authUserId}`, timeEntryId);

	return activeTimer;
}

export async function getActiveTimerForUser(authUserId: string): Promise<any> {
	const timeEntryId = await redisClient.get(`active_timer:${authUserId}`);
	if (!timeEntryId) {
		return null;
	}

	const [timeEntry] = await db.select().from(time_entries).where(eq(time_entries.id, timeEntryId));

	if (!timeEntry) {
		return null;
	}

	const activeTimer = {
		auth_user_id: authUserId,
		time_entry: timeEntry
	};

	return activeTimer;
}

export async function stopUserTimer(authUserId: string): Promise<any> {
	const timeEntryId = await redisClient.get(`active_timer:${authUserId}`);

	if (!timeEntryId) {
		throw new Error('No active timer found for user');
	}

	// Update the time entry with end_time
	const [timeEntry] = await db
		.update(time_entries)
		.set({ end_time: new Date() })
		.where(and(eq(time_entries.auth_user_id, authUserId), eq(time_entries.id, timeEntryId)))
		.returning();

	// Remove from active timers
	await redisClient.del(`active_timer:${authUserId}`);

	return timeEntry;
}

// Add pagination, sorting, filtering
export async function getAllTimeEntriesForUser(
	authUserId: string,
	page = 1,
	pageSize = 10,
	sort = 'start_time:desc',
	filters: { field: string; operator: string; value: any }[] = []
): Promise<{ entries: (typeof time_entries.$inferSelect)[]; total: number }> {
	const whereConditions = [
		eq(time_entries.auth_user_id, authUserId),
		isNotNull(time_entries.end_time)
	];

	// Map operators to Drizzle functions
	const operatorMap = {
		eq: eq,
		gt: gt,
		lt: lt,
		gte: gte,
		lte: lte
	};

	// Apply filters
	for (const filter of filters) {
		const { field, operator, value } = filter;
		if (operator === 'between') {
			// @ts-expect-error
			whereConditions.push(between(time_entries[field], value[0], value[1]));
		} else {
			// @ts-expect-error
			const drizzleFunc = operatorMap[operator];
			if (drizzleFunc) {
				// @ts-expect-error
				whereConditions.push(drizzleFunc(time_entries[field], value));
			} else {
				throw new Error(`Invalid operator: ${operator}`);
			}
		}
	}

	// Total count
	const totalQuery = db
		.select({ count: count() })
		.from(time_entries)
		.where(and(...whereConditions));
	const totalResult = await totalQuery;
	const total = totalResult[0].count;

	// Build and execute the query for entries
	let entriesQuery = db
		.select()
		.from(time_entries)
		.where(and(...whereConditions));

	// Sorting
	const [sortField, sortDirection] = sort.split(':');
	if (sortDirection.toLowerCase() === 'asc') {
		// @ts-expect-error
		entriesQuery = entriesQuery.orderBy(asc(time_entries[sortField]));
	} else {
		// @ts-expect-error
		entriesQuery = entriesQuery.orderBy(desc(time_entries[sortField]));
	}

	// Pagination
	const offset = (page - 1) * pageSize;
	// @ts-expect-error
	entriesQuery = entriesQuery.limit(pageSize).offset(offset);

	const entries = await entriesQuery;
	return { entries, total };
}

export async function getTimeEntryById(
	timeEntryId: string
): Promise<(typeof time_entries.$inferSelect)[]> {
	return await db.select().from(time_entries).where(eq(time_entries.id, timeEntryId));
}

export async function updateTimeEntry({
	authUserId,
	timeEntryId,
	projectId,
	start_time,
	end_time,
	description,
	tags
}: {
	authUserId: string;
	timeEntryId: string;
	projectId?: string | null;
	start_time?: Date;
	end_time?: Date;
	description?: string;
	tags?: string[];
}): Promise<(typeof time_entries.$inferSelect)[]> {
	// Fetch the current time entry from the database
	const existingEntry = await db.query.time_entries.findFirst({
		where: and(
			eq(time_entries.id, timeEntryId),
			eq(time_entries.auth_user_id, authUserId)
		)
	});

	if (!existingEntry) {
		throw new Error('Time entry not found');
	}

	// Validation: prevent overwriting defined values with null/undefined
	if (existingEntry.start_time && (start_time === null || start_time === undefined)) {
		throw new Error('Cannot unset start_time that is already defined');
	}
	if (existingEntry.end_time && (end_time === null || end_time === undefined)) {
		throw new Error('Cannot unset end_time that is already defined');
	}

	// If start_time is bigger than current time, set it to current time
	if (start_time && start_time > new Date()) {
		start_time = new Date();
	}

	// Proceed with update
	return await db
		.update(time_entries)
		.set({
			project_id: projectId,
			start_time: start_time,
			end_time: end_time,
			description,
			tags
		})
		.where(and(eq(time_entries.id, timeEntryId), eq(time_entries.auth_user_id, authUserId)))
		.returning();
}
